<template>
  <el-dropdown trigger="click" class="user-dropdown" @command="handleCommand">
    <div class="user-avatar-container">
      <el-avatar :size="32" class="user-avatar">
        {{ userInitials }}
      </el-avatar>
      <span class="user-name" v-if="!isMobile">
        {{ userName }}
        <el-icon class="user-dropdown-icon"><ArrowDown /></el-icon>
      </span>
    </div>
    
    <template #dropdown>
      <el-dropdown-menu class="user-dropdown-menu">
        <div class="user-dropdown-header">
          <el-avatar :size="40" class="user-dropdown-avatar">
            {{ userInitials }}
          </el-avatar>
          <div class="user-dropdown-info">
            <div class="user-dropdown-name">{{ userName }}</div>
            <div class="user-dropdown-role">{{ authStore.user?.roleName || '管理员' }}</div>
          </div>
        </div>
        <div class="user-dropdown-divider"></div>
        <el-dropdown-item command="profile">
          <el-icon><User /></el-icon>
          <span>个人信息</span>
        </el-dropdown-item>
        <el-dropdown-item command="password">
          <el-icon><Key /></el-icon>
          <span>修改密码</span>
        </el-dropdown-item>
        <el-dropdown-item command="settings">
          <el-icon><Setting /></el-icon>
          <span>偏好设置</span>
        </el-dropdown-item>
        <div class="user-dropdown-divider"></div>
        <el-dropdown-item command="logout">
          <el-icon><SwitchButton /></el-icon>
          <span>退出登录</span>
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { useAuthStore } from '../store/auth';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { User, Key, Setting, SwitchButton, ArrowDown } from '@element-plus/icons-vue';

const authStore = useAuthStore();
const router = useRouter();

// 获取用户名
const userName = computed(() => {
  return authStore.user?.username || '未登录';
});

// 获取用户名首字母作为头像
const userInitials = computed(() => {
  const name = userName.value;
  if (name === '未登录') return '游';
  
  // 提取中文名字首字或英文名字首字母
  const isChinese = /[\u4e00-\u9fa5]/.test(name);
  if (isChinese) {
    return name.charAt(0);
  } else {
    return name.charAt(0).toUpperCase();
  }
});

// 检测是否为移动设备
const isMobile = computed(() => {
  return window.innerWidth <= 768;
});

// 处理下拉菜单命令
const handleCommand = async (command: string) => {
  switch(command) {
    case 'profile':
      router.push('/profile');
      break;
    case 'password':
      router.push('/change-password');
      break;
    case 'settings':
      router.push('/user-settings');
      break;
    case 'logout':
      try {
        await ElMessageBox.confirm(
          '确定要退出登录吗？',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        );
        // 使用auth store的logoutAction，会调用后端API
        await authStore.logoutAction();
        router.push('/login');
      } catch {
        // 用户取消操作
      }
      break;
  }
};

// 监听窗口大小变化
const initResizeListener = () => {
  window.addEventListener('resize', () => {
    // 这里可以添加窗口大小变化的处理逻辑
  });
};

// 组件初始化
initResizeListener();
</script>

<style scoped>
.user-dropdown {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.user-avatar-container {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 20px;
  transition: all 0.2s ease;
}

.user-avatar-container:hover {
  background-color: var(--bg-color-tertiary);
}

.user-avatar {
  background-color: var(--primary-color);
  color: white;
  font-weight: 500;
}

.user-name {
  margin-left: 8px;
  font-size: 14px;
  color: var(--text-color-primary);
  display: flex;
  align-items: center;
  font-weight: 500;
}

.user-dropdown-icon {
  margin-left: 4px;
  font-size: 12px;
  color: var(--text-color-secondary);
  transition: transform 0.2s ease;
}

:deep(.user-dropdown-menu) {
  width: 220px;
  padding: 8px 0;
  margin: 4px 0;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border-color-light);
}

.user-dropdown-header {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, var(--primary-color-light), var(--primary-color));
  margin: -8px -0 8px -0;
  border-radius: 8px 8px 0 0;
}

.user-dropdown-avatar {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  font-weight: 500;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.user-dropdown-info {
  margin-left: 12px;
  flex: 1;
}

.user-dropdown-name {
  font-weight: 500;
  font-size: 14px;
  color: white;
  margin-bottom: 2px;
}

.user-dropdown-role {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
}

.user-dropdown-divider {
  height: 1px;
  background-color: var(--border-color-lighter);
  margin: 8px 0;
}

:deep(.el-dropdown-menu__item) {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  font-size: 14px;
  color: var(--text-color-primary);
  border-radius: 4px;
  margin: 0 8px;
  transition: all 0.2s ease;
}

:deep(.el-dropdown-menu__item:hover) {
  background-color: var(--primary-color-light);
  color: var(--primary-color);
}

:deep(.el-dropdown-menu__item .el-icon) {
  margin-right: 8px;
  font-size: 16px;
}

:deep(.el-dropdown-menu__item:last-child) {
  color: var(--danger-color);
}

:deep(.el-dropdown-menu__item:last-child:hover) {
  background-color: var(--danger-color-light);
  color: var(--danger-color);
}

:deep(.el-dropdown-menu__item:last-child .el-icon) {
  color: var(--danger-color);
}

/* 下拉动画效果 */
:deep(.el-dropdown__popper) {
  transform-origin: top center;
}

.user-dropdown:hover .user-dropdown-icon {
  transform: rotate(180deg);
}
</style>